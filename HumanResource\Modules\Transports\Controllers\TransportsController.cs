using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Transports.Models.Entities;
using HumanResource.Modules.Transports.ViewModels;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Core.Helpers;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Contexts;
using HumanResource.Core.UI.Models;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Modules.Purchases.Models.Entities;

// Assuming your DbContext is 'hrmsContext' and it has a DbSet for 'Transport'

namespace HumanResource.Modules.Transports.Controllers;


[Area("Transports")]
[Route("Transports")]
public class TransportsController : BaseController
{


	public  TransportsViewModel _v;

	public TransportsController(
        hrmsContext context, 
        IHttpContextAccessor httpContextAccessor, AppHelper helper) 
        : base(context, httpContextAccessor, helper)
	{
		
        _v = new TransportsViewModel(context, httpContextAccessor, helper);

        _v.Page.Active = "transports";
        _v.Helper = helper;
        _v.Auth = Auth();

    }

    [Route("Index")]
    public IActionResult Index()
    {

        if (!Can("transports-admin|transports-department|transports-dgeneral"))
            return StatusCode(403);

        _v.Page.Class = " ";
        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
             new Breadcrumb {Label="Transports", Url=$"/Transports/Index"},
        };

        _v.Cars = _context.Cars.Where(car => car.Deleted01 != 1).ToList();
        return View(_v);
    }


    [Route("Manager")]
    public IActionResult ManagerApproval()
    {
        if (!Can("transports-admin|department-manager"))
            return StatusCode(403);

    
        _v.Page.Reload = true;
        _v.Page.Active = "ManagerApprovals";

        _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        var fromQueryString = HttpContext.Request.Query["from"].ToString();
        var toQueryString = HttpContext.Request.Query["to"].ToString();


        if (!string.IsNullOrEmpty(fromQueryString))
        {
            if (DateTime.TryParse(fromQueryString, out DateTime from))
            {
                _v.Page.Filter.DateFrom = from;
            }
        }

        if (!string.IsNullOrEmpty(toQueryString))
        {
            if (DateTime.TryParse(toQueryString, out DateTime to))
            {
                to = to.Date.Add(new TimeSpan(23, 59, 59));
                _v.Page.Filter.DateTo = to;
            }
        }

        IEnumerable<CarsRequests> carsRequests = new List<CarsRequests>();

        var carsRequestsDepartmentManager = _context.CarsRequests
        .Include(car => car.Car)
        .Where(
            r => r.DepManagerId == Auth().EmpNo
            && r.Deleted01 != 1
            && r.CreatedAt >= _v.Page.Filter.DateFrom
            && r.CreatedAt <= _v.Page.Filter.DateTo
            && _context.VempDtls.Any(s => s.EmpNo == r.StaffId)
            )
        .ToList();

        _v.CarsRequests = carsRequestsDepartmentManager;


        return View(_v);

    }

    [Route("Manager/View/{cId}")]
    public IActionResult ManagerView(string cId)
    {
        if (!Can("transports-admin|department-manager"))
            return StatusCode(403);

        int id = DcInt(cId);

        _v.Page.Reload = true;
        _v.Page.Layout = "_Popup";
        _v.Page.Active = "ManagerApprovals";

        _v.CarsRequest = _context.CarsRequests
            .Include(car => car.Car)
            // .Include(staff => staff.Staff)
            .Where(r => r.Id == id && r.DepManagerId == Auth().EmpNo)
            .FirstOrDefault();
        _v.Cars = _context.Cars.Where(r => r.Deleted01 != 1 && r.Status != "Unavailable").ToList();
        _v.StaffList = _context.VempDtls.ToList();
        _v.CarsRequest.Staff = _context.VempDtls.Where(s => s.EmpNo == _v.CarsRequest.StaffId).FirstOrDefault();
        _v.Auth = Auth();

        if (_v.CarsRequest.Status == "Approved")
        {
            _v.CarsRequest.ApprovedByStaff = _context.VempDtls
                .FirstOrDefault(r => r.EmpNo == _v.CarsRequest.ApprovedBy);
        }

        return View("ViewRequest",_v);
    }


    [Route("Dg")]
    public IActionResult DgApproval()
    {
        if (!Can("transports-admin|transports-dgeneral"))
            return StatusCode(403);


        _v.Page.Reload = true;
        _v.Page.Active = "DgApprovals";

        _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        var fromQueryString = HttpContext.Request.Query["from"].ToString();
        var toQueryString = HttpContext.Request.Query["to"].ToString();


        if (!string.IsNullOrEmpty(fromQueryString))
        {
            if (DateTime.TryParse(fromQueryString, out DateTime from))
            {
                _v.Page.Filter.DateFrom = from;
            }
        }

        if (!string.IsNullOrEmpty(toQueryString))
        {
            if (DateTime.TryParse(toQueryString, out DateTime to))
            {
                to = to.Date.Add(new TimeSpan(23, 59, 59));
                _v.Page.Filter.DateTo = to;
            }
        }

        var carsRequestsDG = _context.CarsRequests
            .Include(car => car.Car)
            .Where(
                r => r.Deleted01 != 1
                && r.ManagerApprov == 1
                && r.Type == "1"
                && r.CreatedAt >= _v.Page.Filter.DateFrom
                && r.CreatedAt <= _v.Page.Filter.DateTo
                && _context.VempDtls.Any(s => s.EmpNo == r.StaffId)
             )
            .ToList();

        _v.CarsRequests = carsRequestsDG;

        return View(_v);

    }

    [Route("Dg/View/{cId}")]
    public IActionResult DgView(string cId)
    {
        if (!Can("transports-admin|department-manager"))
            return StatusCode(403);

        int id = DcInt(cId);

        _v.Page.Reload = true;
        _v.Page.Layout = "_Popup";
        _v.Page.Active = "DgApprovals";

        _v.CarsRequest = _context.CarsRequests
            .Include(car => car.Car)
            // .Include(staff => staff.Staff)
            .Where(r => r.Id == id &&  r.Type == "1" && r.ManagerApprov == 1)
            .FirstOrDefault();
        _v.Cars = _context.Cars.Where(r => r.Deleted01 != 1 && r.Status != "Unavailable").ToList();
        _v.StaffList = _context.VempDtls.ToList();
        _v.CarsRequest.Staff = _context.VempDtls.Where(s => s.EmpNo == _v.CarsRequest.StaffId).FirstOrDefault();
        _v.Auth = Auth();

        if (_v.CarsRequest.Status == "Approved")
        {
            _v.CarsRequest.ApprovedByStaff = _context.VempDtls
                .FirstOrDefault(r => r.EmpNo == _v.CarsRequest.ApprovedBy);
        }

        return View("ViewRequest", _v);
    }


    [Route("Gas")]
    public IActionResult Gas()
    {

		if (!Can("transports-admin|transports-department"))
			return StatusCode(403);

		_v.Page.Class = " ";
		_v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
             new Breadcrumb {Label="Transports", Url=$"/Transports/Index"},
             new Breadcrumb {Label="Gas", Url=$"/Transports/Index"},
        };

		_v.CarsGasDtl = _context.CarsGasDtl.Include(car => car.Car).Where(gas => gas.Deleted01 != 1 && gas.CarId>=1).ToList();
		_v.Cars = _context.Cars.Where(car => car.Deleted01 != 1).ToList();

        return View(_v);
    }

    [Route("Service")]
    public IActionResult Service()
    {

		if (!Can("transports-admin|transports-department"))
			return StatusCode(403);


		_v.Page.Class = " ";
		_v.Page.Reload = true;
		_v.Page.Active = "transports";

		_v.Page.Breadcrumb = new List<Breadcrumb> {
             new Breadcrumb {Label="Transports", Url=$"/Transports/Index"},
             new Breadcrumb {Label="Maintenance", Url=$"/Transports/Index"},
        };

		_v.CarsServices = _context.CarsServices.Include(car => car.Car).Where(gas => gas.Deleted01 != 1).ToList();
		_v.Cars = _context.Cars.Where(car => car.Deleted01 != 1).ToList();

        return View(_v);
    }

    [Route("Violations")]
    public IActionResult Violations()
    {
		if (!Can("transports-admin|transports-department"))
			return StatusCode(403);


		_v.Page.Class = " ";
		_v.Page.Reload = true;
		_v.Page.Active = "transports";

		_v.Page.Breadcrumb = new List<Breadcrumb> {
             new Breadcrumb {Label="Transports", Url=$"/Transports/Index"},
             new Breadcrumb {Label="Violations", Url=$"/Transports/Index"},
        };

		_v.CarsViolations = _context.CarsViolations.Include(car => car.Car).Where(v => v.Deleted01 != 1).ToList();
		_v.Cars = _context.Cars.Where(car => car.Deleted01 != 1).ToList();
		_v.StaffList = _context.VempDtls.ToList();

        return View(_v);
    }

    [Route("Violations/View/{id}")]
    public IActionResult ViewViolationRecord(int? id)
    {

        if (!Can("transports-admin|transports-department"))
            return StatusCode(403);


        if (id == null)
        {
            return NotFound();
        }

		_v.ViolationRecord = _context.CarsViolations.Where(g => g.Id == id && g.Deleted01 != 1).FirstOrDefault();

        if (_v.ViolationRecord == null)
        {
            return NotFound();
        }

		_v.Car = _context.Cars.Where(c => c.CarId == _v.ViolationRecord.CarId).FirstOrDefault();
		_v.Cars = _context.Cars.Where(car => car.Deleted01 != 1).ToList();
        _v.StaffList = _context.VempDtls.ToList();

        return View(_v);
    }

    [Route("Service/View/{id}")]
    public IActionResult ViewServiceRecord(int? id)
    {
        if (!Can("transports-admin|transports-department"))
            return StatusCode(403);


        if (id == null)
        {
            return NotFound();
        }

		_v.ServiceRecord = _context.CarsServices.Where(g => g.Id == id && g.Deleted01 != 1).FirstOrDefault();

        if (_v.ServiceRecord == null)
        {
            return NotFound();
        }
		_v.Car = _context.Cars.Where(c => c.CarId == _v.ServiceRecord.CarId).FirstOrDefault();
		_v.Cars = _context.Cars.Where(car => car.Deleted01 != 1).ToList();


        return View(_v);
    }

    [Route("ViewCar/{id}")]
    public IActionResult ViewCar(int id)
    {

		if (!Can("transports-admin|transports-department"))
			return StatusCode(403);

		_v.Page.Class = " sidebar-xs ";
		_v.Page.Reload = true;
		_v.Page.Back = $"/Transports/Index";

		_v.Page.Breadcrumb = new List<Breadcrumb> {
             new Breadcrumb {Label="Transports", Url=$"/Transports/Index"},
             new Breadcrumb {Label="View", Url=""},
        };

        
        
        var car = _context.Cars
            .Include(g => g.CarsGasDtl)
            .Include(v => v.CarsViolations)
            .Include(c => c.CarsServices)
            .Where(m => m.CarId == id)
            .FirstOrDefault();

        car.TotalServiceCost = car.CarsServices.Where(s => s.Deleted01 != 1).Sum(s => s.Cost);
        car.CarsServices = car.CarsServices.Where(s => s.Deleted01 != 1).ToList();
        car.CarsGasDtl = car.CarsGasDtl.Where(s => s.Deleted01 != 1).ToList();
		car.TotalGasCost = car.CarsGasDtl.Where(s => s.Deleted01 != 1).Sum(s => s.Cost);
		car.CarsViolations = car.CarsViolations.Where(s => s.Deleted01 != 1).ToList();

        if (car == null)
        {
            return NotFound();
        }

		_v.Car = car;

		_v.CarsRequests = _context.CarsRequests.Where(r=> r.CarId == id && r.Deleted01 != 1 && _context.VempDtls.Any(s=>s.EmpNo== r.StaffId)).ToList();

        if (car.Status == "Unavailable" && car.StaffId != 0 )
        {
            _v.Car.Staff = _context.VempDtls
                .Where(s => s.EmpNo == car.StaffId)
                .FirstOrDefault();

			_v.CarsRequest = _context.CarsRequests
				.Where(r => r.StaffId == car.StaffId && r.Status=="Approved")
				.FirstOrDefault();

		}
        _v.StaffList = _context.VempDtls.ToList();
        return View(_v);
    }


    [Route("Gas/View/{id}")]
    public IActionResult ViewGasRecord(int? id)
    {
        if (id == null)
        {
            return NotFound();
        }

		_v.GasRecord = _context.CarsGasDtl
            .Where(g => g.Id == id && g.Deleted01 != 1)
            .FirstOrDefault();

        if (_v.GasRecord == null)
        {
            return NotFound();
        }
		_v.Car = _context.Cars.Where(c => c.CarId == _v.GasRecord.CarId).FirstOrDefault();
		_v.Cars = _context.Cars.Where(car => car.Deleted01 != 1).ToList();


        return View(_v);
    }

    [Route("AllRequests")]
    public IActionResult AllRequests()
    {

		if (!Can("transports-admin|transports-department"))
			return StatusCode(403);

		_v.Page.Class = " sidebar-xs ";
		_v.Page.Reload = true;

		_v.Page.Breadcrumb = new List<Breadcrumb> {
             new Breadcrumb {Label="Transports", Url=$"/Transports/Index"},
             new Breadcrumb {Label="Requests", Url=$"/Transports/Index"},
        };

		_v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
		_v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        var fromQueryString = HttpContext.Request.Query["from"].ToString();
        var toQueryString = HttpContext.Request.Query["to"].ToString();

        
        if (!string.IsNullOrEmpty(fromQueryString))
        {
            if (DateTime.TryParse(fromQueryString, out DateTime from))
            {
				_v.Page.Filter.DateFrom = from;
            }
        }

        if (!string.IsNullOrEmpty(toQueryString))
        {
            if (DateTime.TryParse(toQueryString, out DateTime to))
            {
                to = to.Date.Add(new TimeSpan(23, 59, 59));
				_v.Page.Filter.DateTo = to;
            }
        }

		IEnumerable<CarsRequests> carsRequests = new List<CarsRequests>();


		if (Can("department-manager"))
        {
			var carsRequestsDepartmentManager = _context.CarsRequests
			.Include(car => car.Car)
			.Where(
                r => r.DepManagerId == Auth().EmpNo
                && r.Deleted01 != 1 
                && r.CreatedAt >= _v.Page.Filter.DateFrom 
                && r.CreatedAt <= _v.Page.Filter.DateTo
                && _context.VempDtls.Any(s => s.EmpNo == r.StaffId)
             )
			.ToList();

			carsRequests = carsRequests.Union(carsRequestsDepartmentManager);
		}



		if (Can("transports-dgeneral"))
		{
			var carsRequestsDG = _context.CarsRequests
			.Include(car => car.Car)
			.Where(
				r => r.Deleted01 != 1
				&& r.ManagerApprov == 1
				&& r.Type == "1"
				&& r.CreatedAt >= _v.Page.Filter.DateFrom
				&& r.CreatedAt <= _v.Page.Filter.DateTo
                && _context.VempDtls.Any(s => s.EmpNo == r.StaffId)
             )
			.ToList();

			carsRequests = carsRequests.Union(carsRequestsDG);
		}

		if (Can("transports-department"))
		{
			var carsRequestsDp = _context.CarsRequests
			.Include(car => car.Car)
			.Where(
				r => r.Deleted01 != 1
				&& r.CreatedAt >= _v.Page.Filter.DateFrom
				&& r.CreatedAt <= _v.Page.Filter.DateTo
                && _context.VempDtls.Any(s => s.EmpNo == r.StaffId)
                && (r.Type == "1" && r.DGeneralApprov == 1 || r.Type == "0")
			 )
			.ToList();

			carsRequests = carsRequests.Union(carsRequestsDp);
		}

		if (Can("transports-admin"))
		{
			var carsRequestsadmin = _context.CarsRequests
			.Include(car => car.Car)
			.Where(
				r => r.Deleted01 != 1
				&& r.CreatedAt >= _v.Page.Filter.DateFrom
				&& r.CreatedAt <= _v.Page.Filter.DateTo
                && _context.VempDtls.Any(s => s.EmpNo == r.StaffId)
             )
			.ToList();

			carsRequests = carsRequests.Union(carsRequestsadmin);
		}

		_v.Cars = _context.Cars.Where(r => r.Deleted01 != 1).ToList();
		_v.StaffList = _context.VempDtls.ToList();
        _v.CarsRequests = carsRequests.ToList();


		return View(_v);
    }


    [Route("AllDatatable")]
    [HttpPost]
    public IActionResult AllRequestsDatatable([FromForm] DataTableHelper datatable)
    {

        if (!Can("transports-admin|transports-department"))
            return StatusCode(403);

        _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        var fromQueryString = HttpContext.Request.Query["from"].ToString();
        var toQueryString = HttpContext.Request.Query["to"].ToString();


        if (!string.IsNullOrEmpty(fromQueryString))
        {
            if (DateTime.TryParse(fromQueryString, out DateTime from))
            {
                _v.Page.Filter.DateFrom = from;
            }
        }

        if (!string.IsNullOrEmpty(toQueryString))
        {
            if (DateTime.TryParse(toQueryString, out DateTime to))
            {
                to = to.Date.Add(new TimeSpan(23, 59, 59));
                _v.Page.Filter.DateTo = to;
            }
        }


        var query = _db.CarsRequests.Include(car => car.Car).Where(
                r => r.Deleted01 != 1
                && r.CreatedAt >= _v.Page.Filter.DateFrom
                && r.CreatedAt <= _v.Page.Filter.DateTo
                && _context.VempDtls.Any(s => s.EmpNo == r.StaffId)
             );

        if (!string.IsNullOrEmpty(datatable.Search.Value))
        {
            query = query.Where(f => 
            f.StaffId.ToString().Contains(datatable.Search.Value) 
            || f.Id.ToString().Contains(datatable.Search.Value)
            || f.FromDate.ToString().Contains(datatable.Search.Value)
            || f.ToDate.ToString().Contains(datatable.Search.Value)
            || f.Car.Name.Contains(datatable.Search.Value)
            || f.Car.Code.Contains(datatable.Search.Value)
            || f.Status.Contains(datatable.Search.Value)

            );
        }

        var total = query.Count();

        switch (datatable.Order[0].Column)
        {

            case 1:
                if (datatable.Order[0].Dir == "asc")
                {
                    query = query.OrderByDescending(r => r.StaffId);
                }
                else
                {
                    query = query.OrderBy(r => r.StaffId);
                }
                break;

            case 2:
                if (datatable.Order[0].Dir == "asc")
                {
                    query = query.OrderByDescending(r => r.Car.Code);
                }
                else
                {
                    query = query.OrderBy(r => r.Car.Code);
                }
                break;
            case 3:
                if (datatable.Order[0].Dir == "asc")
                {
                    query = query.OrderByDescending(r => r.CreatedAt);
                }
                else
                {
                    query = query.OrderBy(r => r.CreatedAt);
                }
                break;
            case 5:
                if (datatable.Order[0].Dir == "asc")
                {
                    query = query.OrderByDescending(r => r.ToDate);
                }
                else
                {
                    query = query.OrderBy(r => r.ToDate);
                }
                break;
            case 6:
                if (datatable.Order[0].Dir == "asc")
                {
                    query = query.OrderByDescending(r => r.Status);
                }
                else
                {
                    query = query.OrderBy(r => r.Status);
                }
                break;
            default:
                if (datatable.Order[0].Dir == "asc")
                {
                    query = query.OrderByDescending(r => r.Id);
                }
                else{
                    query = query.OrderBy(r => r.Id);
                }
                
                break;
        }

        var data = query.Skip(datatable.Start).Take(datatable.Length).ToList();

        var table = data.Select(ro => new
        {
          

            id = "<a href='/Transports/ViewRequest/" + ro.Id + "'>#" + ro.Id + "</a>",
            emp = "#"+ro.StaffId+ "<br>" + _h.StaffData(ro.StaffId).EmpNameA,
            car = ro.Car != null ? ro.Car.Plate +"<br>"+ ro.Car.Name : "--",
            date = _v._dt(ro.CreatedAt),

            fromTo = "<p class='text-center'><span dir=\"ltr\" class=\"badge badge-dark rounded-pill px-2 \"><strong>" + _v._dt(ro.FromDate) + "</strong></span> <br> <i class=\"fas fa-arrow-down\"></i> <br>" +
            (
            ro.ToDate > DateTime.Now ? 
                ro.Status != "Completed" ? "<span dir=\"ltr\" class=\"badge badge-danger rounded-pill px-2 \"><strong>"+_v._dt(ro.ToDate)+"</strong></span>" 
                    : "<span dir=\"ltr\" class=\"badge badge-success rounded-pill px-2 \"><strong>"+_v._dt(ro.ToDate)+"</strong></span>" 
                    : "<span dir=\"ltr\" class=\"badge badge-"+(ro.Status=="Completed"?"success":"info")+" rounded-pill px-2 shadow\"><strong>"+_v._dt(ro.ToDate)+"</strong></span>") + "</p>",
           

            status = "<span class=\"badge  rounded-pill px-2  badge-"+(ro.Status=="Completed"?"success":"danger")+" \"><strong class=\"\">"+_v._(ro.Status)+"</strong></span>",

        }).ToList();

    

        var output = new
        {
            datatable.Draw,
            recordsTotal = total,
            recordsFiltered = string.IsNullOrEmpty(datatable.Search.Value) ? total : table.Count,
            data = table
        };


        return Json(output);

    }


    [Route("ViewRequest/{id}")]
	public IActionResult ViewRequest(int? id)
    {
		if (!Can("transports-admin|transports-department|transports-dgeneral"))
			return StatusCode(403);


		_v.Page.Class = " sidebar-xs ";
		_v.Page.Reload = true;
		_v.Page.Back = $"/Transports/AllRequests";

		_v.Page.Breadcrumb = new List<Breadcrumb> {
             new Breadcrumb {Label="طلبات السيارات", Url=$"/Transports/Index"},
             new Breadcrumb {Label="عرض الطلب", Url=$"/Transports/AllRequests"},
             new Breadcrumb {Label="عرض", Url=$"/Transports/Index"},
        };

		_v.CarsRequest = _context.CarsRequests
            .Include(car => car.Car)
            // .Include(staff => staff.Staff)
            .Where(r => r.Id == id )
            .FirstOrDefault();
		_v.Cars = _context.Cars.Where(r => r.Deleted01 != 1 && r.Status!= "Unavailable").ToList();
		_v.StaffList = _context.VempDtls.ToList();
        _v.CarsRequest.Staff = _context.VempDtls.Where(s => s.EmpNo == _v.CarsRequest.StaffId).FirstOrDefault();
        _v.Auth = Auth();

        if (_v.CarsRequest.Status == "Approved")
        {
			_v.CarsRequest.ApprovedByStaff = _context.VempDtls
                .FirstOrDefault(r => r.EmpNo == _v.CarsRequest.ApprovedBy);
		}

        return View(_v);
    }

    [HttpPost("CreateRequest")]
    public IActionResult CreateRequest(CarsRequests model)
    {

        if(!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }

        if ((model.FromDate < DateTime.Now || !Can("transports-department|transports-admin"))  && model.ToDate < model.FromDate)
        {
            var response = new
            {
                success = false,
                message = new List<string> {_v._("Invalid time") },
                action = "",
            };

            return Json(response);
        }

        var StaffId = Auth().EmpNo;
        int? CarId = null;
        var Status = "New";
        var CurrentOdo = "0";
        var Note = "";
        var Type = "0";
        var DepManagerId = model.DepManagerId;
        var ManagerApprov = 0;


        var Managers = _h.Managers(_v.Profile.EmpNo);


        DateTime FromDate = model.FromDate;
        DateTime ToDate = model.ToDate;
        DateTime SpTime = new DateTime(FromDate.Year,FromDate.Month,FromDate.Day,14,30,0);

        int? ApprovedBy = null;
		DateTime? ApprovedAt = null;


        if (ToDate> SpTime)
            Type = "1";
        

		if (Can("transports-admin|transports-department") && model.StaffId>1)
            StaffId = model.StaffId;
            Note = model.Note;

        if (Can("transports-dgeneral"))
        {
            DepManagerId  = Auth().EmpNo.Value;
            Status = "Approved by department manager";
            ManagerApprov = 1;
        }

        var NewRequest = new CarsRequests
        {
            StaffId = StaffId,
            CarId = CarId,
            DepManagerId = DepManagerId,
			FromDate = model.FromDate,
            ToDate = model.ToDate,
            Note = Note,
            Reason = model.Reason,
            CurrentOdo  = CurrentOdo,
            Status  = Status,
            Type  = Type,
            ApprovedBy = ApprovedBy,
			ApprovedAt = ApprovedAt,
            ManagerApprov = ManagerApprov,
			CreatedBy = Auth().EmpNo,
            CreatedAt = DateTime.UtcNow                  
        };

        

        try
        {

			_context.CarsRequests.Add(NewRequest);
            _context.SaveChanges();

            var response = new
            {
                success = true,
                message = new List<string> { "Request created successfully." },
                action = "reload",
            };

            if (Can("transports-dgeneral"))
            {
                 response = new
                {
                    success = true,
                    message = new List<string> { "Request created successfully." },
                    action = $"location.href='/Transports/ViewRequest/{NewRequest.Id}'",
                };
            }

            

            return Json(response);
        }
        catch (Exception ex)
        {

            var response = new
            {
                success = false,
                message = new List<string> { $"Error: {ex.InnerException}" },
                action = "",  
            };

            return Json(response);
        }
    }


	[HttpPost("UpdateRequest")]
	public IActionResult UpdateRequest(CarsRequests model)
	{


        if (!Can("transports-admin|transports-department"))
            return StatusCode(403);

        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }



        var identity = Identity;

		var requestToUpdate = _context.CarsRequests.FirstOrDefault(a => a.Id == model.Id);

        if(requestToUpdate == null)
            return StatusCode(404);


        var Status = model.Status;
		var CarId = model.CarId;


        requestToUpdate.StaffId= model.StaffId;
        requestToUpdate.DepManagerId= model.DepManagerId;
        requestToUpdate.CurrentOdo = model.CurrentOdo;
		requestToUpdate.CarId = model.CarId;
        requestToUpdate.FromDate = model.FromDate;
        requestToUpdate.ToDate = model.ToDate;
        requestToUpdate.Reason = model.Reason;
        requestToUpdate.Note = model.Note;


		requestToUpdate.UpdatedAt = DateTime.UtcNow;
        requestToUpdate.UpdatedBy = Auth().EmpNo;


		try
		{
			_context.SaveChangesAsync();

			_context.SaveChanges();


			var response = new
			{
				success = true,
				message = new List<string> { "Request updated successfully." },
				action = "reload",
			};


			return Json(response);
		}
		catch (Exception ex)
		{

			var response = new
			{
				success = false,
				message = new List<string> { $"Error: {ex.Message}" },
				action = "",
			};

			return Json(response);
		}
	}

	
	public IActionResult DepManagerApprove(int? id)
    {



		var requestToUpdate = _context.CarsRequests.FirstOrDefault(a => a.Id == id && (a.DepManagerId==Auth().EmpNo || Can("transports-admin")));


        if (requestToUpdate == null)
            return StatusCode(404);
		


		var Status = "Approved by department manager";

        
		requestToUpdate.Status = Status;
		requestToUpdate.ManagerApprov = 1;

		_context.Update(requestToUpdate);

		_context.SaveChanges();

		var redirectUrl = $"/Transports/AllRequests/?success=true";

		return Redirect(redirectUrl);

	}


    [HttpPost("DGeneralApprove")]
    public IActionResult DGeneralApprove(CarsRequests post)
    {

        if (!Can("transports-admin|transports-dgeneral"))
            return StatusCode(403);


        var requestToUpdate = _context.CarsRequests.FirstOrDefault(a => a.Id == post.Id);

        if (requestToUpdate == null)
            return StatusCode(404);
        

        var Status = "Approved by General Director";

        if (post.CarId > 0)
            requestToUpdate.CarId= post.CarId;

        requestToUpdate.Note = post.Note;

        requestToUpdate.Status = Status;
        requestToUpdate.DGeneralApprov = 1;

        try
        {
            _context.Update(requestToUpdate);

            _context.SaveChanges();


            var response = new
            {
                success = true,
                message = new List<string> { "Request approved successfully." },
                action = "location.replace('/Transports/AllRequests/')",
            };


            return Json(response);
        }
        catch (Exception ex)
        {

            var response = new
            {
                success = false,
                message = new List<string> { $"Error: {ex.Message}" },
                action = "",
            };

            return Json(response);
        }

        


    }

    [HttpPost("DepManagerDecline")]
	public IActionResult DepManagerDecline(CarsRequests model)
	{

	


		var requestToUpdate = _context.CarsRequests.FirstOrDefault(a => a.Id == model.Id);

		if (requestToUpdate == null)
		{
			return StatusCode(404);
		}

        if (requestToUpdate.DepManagerId != Auth().EmpNo && !Can("transports-admin"))
        {
            return StatusCode(404);
        }


		var Status = "Declined by department manager";

		try
		{
			requestToUpdate.Status = Status;
			requestToUpdate.DeclineNote = model.DeclineNote;
			requestToUpdate.ManagerApprov = 2;

			_context.Update(requestToUpdate);

			_context.SaveChanges();

			var response = new
			{
				success = true,
				message = new List<string> { "Request declined successfully." },
				action = "location.href='/Transports/AllRequests/'",
			};


			return Json(response);
		}
		catch (Exception ex)
		{

			var response = new
			{
				success = false,
				message = new List<string> { $"Error: {ex.Message}" },
				action = "",
			};

			return Json(response);
		}
	}

    [HttpPost("DGDecline")]
    public IActionResult DGDecline(CarsRequests model)
    {

        if (!Can("transports-admin|transports-dgeneral"))
            return StatusCode(403);


        var requestToUpdate = _context.CarsRequests.FirstOrDefault(a => a.Id == model.Id);

        if (requestToUpdate == null)
        {
            return StatusCode(404);
        }


        var Status = "Declined by General Director";

        try
        {
            requestToUpdate.Status = Status;
            requestToUpdate.DeclineNote = model.DeclineNote;
            requestToUpdate.DGeneralApprov = 2;

            _context.Update(requestToUpdate);

            _context.SaveChanges();

            var response = new
            {
                success = true,
                message = new List<string> { "Request declined successfully." },
                action = "location.href='/Transports/AllRequests/'",
            };


            return Json(response);
        }
        catch (Exception ex)
        {

            var response = new
            {
                success = false,
                message = new List<string> { $"Error: {ex.Message}" },
                action = "",
            };

            return Json(response);
        }
    }

    [HttpPost("ApproveRequest")]
    public IActionResult ApproveRequest(CarsRequests post)
    {
        if (!Can("transports-admin|transports-department"))
            return StatusCode(403);


        var requestToUpdate = _context.CarsRequests.FirstOrDefault(a => a.Id == post.Id );
        var carToUpdate = _context.Cars.FirstOrDefault(a => a.CarId == post.CarId);

        if (requestToUpdate == null || carToUpdate == null)
        {
            var response = new
            {
                success = false,
                message = new List<string> { "Invalid requrest" },
                action = "",
            };

            return Json(response);
        }


        try
        {
          


            requestToUpdate.Status = "Approved";

            requestToUpdate.CarId = post.CarId;



            requestToUpdate.ApprovedBy = Auth().EmpNo;
            requestToUpdate.ApprovedAt = DateTime.Now;

            _context.SaveChangesAsync();

    
            carToUpdate.CurrentOdo = int.Parse(post.CurrentOdo);

            carToUpdate.Status = "Unavailable";
            carToUpdate.StaffId = requestToUpdate.StaffId;

            _context.Update(carToUpdate);
            _context.Update(requestToUpdate);

            _context.SaveChanges();

            var response = new
            {
                success = true,
                message = new List<string> { "Request approved successfully" },
                action = "reload",
            };

            return Json(response);



        }
        catch (Exception ex)
        {

            var response = new
            {
                success = false,
                message = new List<string> { $"Error: {ex.Message}" },
                action = "",
            };

            return Json(response);
        }
      
    }

    [HttpPost("DeclineRequest")]
    public IActionResult DeclineRequest(CarsRequests model)
    {

        if (!Can("transports-admin|transports-department"))
            return StatusCode(403);


        var requestToUpdate = _context.CarsRequests.FirstOrDefault(a => a.Id == model.Id);

        if (requestToUpdate == null)
        {
            return StatusCode(404);
        }

        if (requestToUpdate.DepManagerId != Auth().EmpNo && !Can("transports-admin"))
        {
            return StatusCode(404);
        }

        try
        {
            requestToUpdate.Status = "Declined";
            requestToUpdate.DeclineNote = model.DeclineNote;
  

            _context.Update(requestToUpdate);

            _context.SaveChanges();

            var response = new
            {
                success = true,
                message = new List<string> { "Request declined successfully." },
                action = "location.href='/Transports/AllRequests/'",
            };


            return Json(response);
        }
        catch (Exception ex)
        {

            var response = new
            {
                success = false,
                message = new List<string> { $"Error: {ex.Message}" },
                action = "",
            };

            return Json(response);
        }


    }



    public IActionResult CancelApproval(int? id)
	{
		if (!_v.can("transports-admin|transports-department"))
			return StatusCode(403);

		var identity = Identity;

		if (id == null)
		{
			return NotFound();
		}

		var request = _context.CarsRequests.Find(id);

		if (request == null)
		{
			return NotFound();
		}


		var carToUpdate = _context.Cars.FirstOrDefault(a => a.CarId == request.CarId);
		carToUpdate.StaffId = 0;
		carToUpdate.Status = "Available";

		_context.Update(carToUpdate);

		request.UpdatedBy = Auth().EmpNo; 
		request.UpdatedAt = DateTime.UtcNow;
		request.Status = "Approval Canceled";
        _context.SaveChangesAsync();
		_context.SaveChanges();

		var redirectUrl = $"/Transports/ViewRequest/{request.Id}";

		return Redirect(redirectUrl);
	}


    [HttpPost("RequestCompleted")]
	public IActionResult RequestCompleted(int? id,CarsRequests post)
	{

        if (!Can("transports-admin|transports-department"))
            return StatusCode(403);

		var identity = Identity;

		if (id == null)
		    return NotFound();
		

		var request = _context.CarsRequests.Find(post.Id);

		if (request == null)
		    return NotFound();
		


		var carToUpdate = _context.Cars.FirstOrDefault(a => a.CarId == request.CarId);
		carToUpdate.StaffId = 0;
		carToUpdate.Status = "Available";
		carToUpdate.CurrentOdo = int.Parse(post.CurrentOdo);



        try
        {

            _context.Update(carToUpdate);

            request.UpdatedBy = Auth().EmpNo;
            request.UpdatedAt = DateTime.UtcNow;
            request.ToDate = post.ToDate;
            request.Note = post.Note;
            request.Status = "Completed";
            _context.SaveChangesAsync();
            _context.SaveChanges();


            var response = new
            {
                success = true,
                message = new List<string> { "Request completed" },
                action = "location.replace('/Transports/AllRequests/')",

            };

            return Json(response);
        }
        catch (Exception ex)
        {

            var response = new
            {
                success = false,
                message = new List<string> { $"Error: {ex.Message}" },
                action = "",

            };

 
            return Json(response);
        }

  
	}

    public IActionResult RequestCompleted2(int? id)
    {

        if (!_v.can("transports-admin|transports-department"))
            return StatusCode(403);

        var identity = Identity;

        if (id == null)
        {
            return NotFound();
        }

        var request = _context.CarsRequests.Find(id);

        if (request == null)
        {
            return NotFound();
        }


        var carToUpdate = _context.Cars.FirstOrDefault(a => a.CarId == request.CarId);
        carToUpdate.StaffId = 0;
        carToUpdate.Status = "Available";

        _context.Update(carToUpdate);

        request.UpdatedBy = Auth().EmpNo;
        request.UpdatedAt = DateTime.UtcNow;
        request.Status = "Completed";
        _context.SaveChangesAsync();
        _context.SaveChanges();

        var redirectUrl = $"/Transports/ViewCar/{carToUpdate.CarId}";

        return Redirect(redirectUrl);
    }

    public IActionResult DeleteRequest(int? id)
	{
		var identity = Identity;

		if (id == null)
		{
			return NotFound();
		}

		var request = _context.CarsRequests.Find(id);

		if (request == null)
		{
			return NotFound();
        }

        if (_v.Can("transports-department|transports-admin|transports-dgeneral"))
        {

            request.Deleted01 = 1;
            request.UpdatedBy = Auth().EmpNo;
			request.UpdatedAt = DateTime.UtcNow;
			request.Status = "Canceled";

			var carToUpdate = _context.Cars.FirstOrDefault(a => a.CarId == request.CarId);
            if (carToUpdate != null)
            {
                carToUpdate.StaffId = null;
                carToUpdate.Status = "Available";

                _context.Update(carToUpdate);
            }
			
			_context.SaveChangesAsync();
			_context.SaveChanges();

			var redirectUrl = $"/Transports/AllRequests";

			return Redirect(redirectUrl);

		}
        else if(Auth().EmpNo == request.CreatedBy)
        {
			request.Deleted01 = 1;
			request.UpdatedBy = Auth().EmpNo;
			request.UpdatedAt = DateTime.UtcNow;
			request.Status = "Canceled by staff";

			var carToUpdate = _context.Cars.FirstOrDefault(a => a.CarId == request.CarId);
			carToUpdate.StaffId = 0;
			carToUpdate.Status = "Available";

			_context.Update(carToUpdate);

			_context.SaveChanges();

			var redirectUrl = $"/Transports/MyRequests";

			return Redirect(redirectUrl);
		}


		return StatusCode(404);
	}



	[HttpPost("CreateCar")]
#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
    public async Task<ActionResult<Cars>> CreateCar( Cars model)
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously
    {
		if (!Can("transports-department|transports-admin"))
			return StatusCode(403);

        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }

        var NewCar = new Cars
        {
            Name = model.Name,
            Plate = model.Plate,
            CurrentOdo = model.CurrentOdo,
            NextOdoService = model.NextOdoService,
            YearModel = model.YearModel,
            GasCardNo = model.GasCardNo,
            Expiry = model.Expiry,
            Vin = model.Vin,
            Description = model.Description,
            Status = "Available",
            CreatedAt = DateTime.UtcNow 
        };

        try
        {

            _context.Cars.Add(NewCar);
            _context.SaveChanges();

            // define the JSON response
            var response = new
            {
                success = true,
                message = new List<string> { "Car created successfully." },
                action = "reload",  
         
            };

            // return as JSON
            return Json(response);
        }
        catch (Exception ex)
        {
            // handle error, log exception, etc.

            // define the JSON response
            var response = new
            {
                success = false,
                message = new List<string> { $"Error: {ex.Message}" },
                action = "",  
               
            };

            // return as JSON
            return Json(response);
        }

    }

    [HttpPost("CreateService")]
    public async Task<ActionResult<CarsServices>> CreateService(CarsServices model)
    {
		if (!Can("transports-department|transports-admin"))
			return StatusCode(403);

		    var NewService = new CarsServices
            {
                CarId = model.CarId,
                CurrentOdo = model.CurrentOdo,
                NextOdoService = model.NextOdoService,
      
                Type = model.Type,
            
                Date = model.Date,
                Description = model.Description,
                LongDescription = model.LongDescription,
                CreatedAt = DateTime.UtcNow 
            };

        
            var carToUpdate = _context.Cars.FirstOrDefault(a => a.CarId == model.CarId);
            carToUpdate.NextOdoService = model.NextOdoService;
            carToUpdate.CurrentOdo = model.CurrentOdo;

            _context.CarsServices.Add(NewService);

            _context.Update(carToUpdate);

            await _context.SaveChangesAsync();

            //Send to Purchases


            
           

            
            var response = new
            {
                success = true,
                message = new List<string> { "Maintenance created successfully." },
                action = "reload",  
               
            };

            
            return Json(response);
        

    }

    [Route("ApproveService/{cId}")]
    public async Task<IActionResult> ApproveService(string cId)
    {

        if (!Can("transports-dgeneral|transports-admin"))
            return StatusCode(403);

        int Id =  DcInt(cId);


        var Service = _db.CarsServices.Find(Id);


        Service.Status = "Sent to Purchases";
        Service.ApprovedBy = _v.Profile.EmpNo.Value;
        Service.ApprovedAt = DateTime.Now;

        _db.Update(Service);
        await _db.SaveChangesAsync();

        var NewRequest = new PurchasesRequest
        {
            RelId = Service.Id,
            RelType = "CarsServices",
            Reference = "Transports service #" + Service.Id,
            Date = DateTime.Now,
            Origin = "Transports Department",
            LastActionDate = DateTime.Now,
        };

        _db.PurchasesRequest.Add(NewRequest);
        await _db.SaveChangesAsync();

        var Car = _db.Cars.Find(Service.CarId);

        var NewItem = new RequestItem
        {

            Name = Service.Type + "[" + Car.Plate + "-" + Car.Name + "]",
            Quantity = 1,
            Note = Service.Description,
            RequestId = NewRequest.Id,
            RelId = Service.Id,
            RelType = "CarsServices",
            CreatedBy = _v.Profile.EmpNo.Value,

        };

        _db.RequestItem.Add(NewItem);
        await _db.SaveChangesAsync();


        return RedirectToAction("Service");


    }

    [HttpPost("CreateGasDtl")]
#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
    public async Task<ActionResult<CarsGasDtl>> CreateGasDtl(CarsGasDtl model)
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously
    {
		if (!Can("transports-department|transports-admin"))
			return StatusCode(403);

        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }

        var carToUpdate = _context.Cars.FirstOrDefault(a => a.CarId == model.CarId);

        if (carToUpdate == null)
            return StatusCode(404);


        var NewGasDtl = new CarsGasDtl
        {
            CarId = model.CarId,
            CurrentOdo = model.CurrentOdo,

            Reference = model.Reference,
  
            Cost = model.Cost,
            Tax = model.Tax,
            Date = model.Date,
            Description = model.Description,
            CreatedAt = DateTime.UtcNow, 
            CreatedBy = Auth().EmpNo.Value,
    };
        try
        {
           
            carToUpdate.CurrentOdo = model.CurrentOdo;

           _context.Update(carToUpdate);
            _context.CarsGasDtl.Add(NewGasDtl);
            _context.SaveChanges();



            // define the JSON response
            var response = new
            {
                success = true,
                message = new List<string> { "Gas record created successfully." },
                action = "reload",  

            };

            // return as JSON
            return Json(response);
        }
        catch (Exception ex)
        {

            var message = "Error: " + ex.Message;

            if(ex.InnerException != null)
            {
                message += "Inner E: " + ex.InnerException.Message;
            }
            var response = new
            {
                success = false,
                message = new List<string> { message },
                action = "",  

            };

            // return as JSON
            return Json(response);
        }

    }

    [HttpPost("CreateViolation")]
#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
    public async Task<ActionResult<CarsViolations>> CreateViolation(CarsViolations model)
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously
    {
		if (!Can("transports-department|transports-admin"))
			return StatusCode(403);

        var carToUpdate = _context.Cars.FirstOrDefault(a => a.CarId == model.CarId);
        carToUpdate.CurrentOdo = model.CurrentOdo;

        var NewViolationDtl = new CarsViolations
        {
            CarId = model.CarId,
            CurrentOdo = model.CurrentOdo,

            Reference = model.Reference,

            Cost = model.Cost,
            Location = model.Location,
            Date = model.Date,
            Description = model.Description,
            StaffId = model.StaffId,
            //PaidAt = model.PaidAt,
            ReceiptNo = model.ReceiptNo,
            CreatedAt = DateTime.UtcNow, 
            CreatedBy = Auth().EmpNo.Value,

        };
        try
        {
            _context.Update(carToUpdate);
            _context.CarsViolations.Add(NewViolationDtl);
            _context.SaveChanges();

  
            var response = new
            {
                success = true,
                message = new List<string> { "Violation record created successfully." },
                action = "reload", 
            };

            // return as JSON
            return Json(response);
        }
        catch (Exception ex)
        {
            var response = new
            {
                success = false,
                message = new List<string> { $"Error: {ex.InnerException}" },
                action = "", 
            };

            return Json(response);
        }
    }


    [HttpPost("UpdateCar")]
    public IActionResult UpdateCar(int? id, Cars model) //int? id, Car model
    {

		if (!Can("transports-admin|transports-department"))
			return StatusCode(403);

	
		var carToUpdate = _context.Cars.FirstOrDefault(a => a.CarId == id);
        if (carToUpdate == null)
            return StatusCode(404);
        

        carToUpdate.Name = model.Name;
        carToUpdate.Plate = model.Plate;
        carToUpdate.CurrentOdo = model.CurrentOdo;
        carToUpdate.NextOdoService = model.NextOdoService;
        carToUpdate.YearModel = model.YearModel;
        carToUpdate.Expiry = model.Expiry;
        carToUpdate.Vin = model.Vin;
        carToUpdate.GasCardNo = model.GasCardNo;
        carToUpdate.Description = model.Description;


        try
        {
            // Save the changes
            _context.SaveChangesAsync();

            var response = new
            {
                success = true,
                message = new List<string> { "Vehicle updated successfully." },
                action = "reload",  
            };

            // return as JSON
            return Json(response);
        }
        catch (Exception ex)
        {
            var response = new
            {
                success = false,
                message = new List<string> { $"Error: {ex.Message}" },
                action = "",  
            };

            // return as JSON
            return Json(response);
        }
    }

    //[HttpPost("{id}")]
    [HttpPost("UpdateService")]
    public IActionResult UpdateService(int? id, CarsServices model) //int? id, Car model
    {

		if (!Can("transports-admin|transports-department"))
			return StatusCode(403);


		
		var serviceToUpdate =  _context.CarsServices.FirstOrDefault(a=>a.Id==id);
        if (serviceToUpdate == null)
            return StatusCode(404);



        serviceToUpdate.CarId = model.CarId;
        serviceToUpdate.Reference = model.Reference;
        serviceToUpdate.Type = model.Type;
        serviceToUpdate.Cost = model.Cost;
        serviceToUpdate.Date = model.Date;
        serviceToUpdate.Description = model.Description;
        serviceToUpdate.LongDescription = model.LongDescription;
        serviceToUpdate.UpdatedBy = Auth().EmpNo;
    

        try
        {
            _context.SaveChangesAsync();

            var response = new
            {
                success = true,
                message = new List<string> { "Maintenance updated successfully." },
                action = "reload",  
            };

            return Json(response);
        }
        catch (Exception ex)
        {
            var response = new
            {
                success = false,
                message = new List<string> { $"Error: {ex.Message}" },
                action = "", 

            };

            return Json(response);
        }
    }

    [HttpPost("UpdateGas")]
    public IActionResult UpdateGas( CarsGasDtl model) //int? id, Car model
    {

		if (!Can("transports-admin|transports-department"))
			return StatusCode(403);


		var gasToUpdate = _context.CarsGasDtl.FirstOrDefault(a => a.Id == model.Id);

        if (gasToUpdate == null)
            return StatusCode(404);



        gasToUpdate.CarId = model.CarId;
        gasToUpdate.Reference = model.Reference;
        gasToUpdate.Cost = model.Cost;
        gasToUpdate.Tax = model.Tax;
        gasToUpdate.Date = model.Date;
        gasToUpdate.Description = model.Description;
        gasToUpdate.UpdatedBy = Auth().EmpNo;
       
        try
        {
            // Save the changes
            _context.SaveChangesAsync();

            var response = new
            {
                success = true,
                message = new List<string> { "Gas record updated successfully." },
                action = "reload",
            };

        
            return Json(response);
        }
        catch (Exception ex)
        {
            var response = new
            {
                success = false,
                message = new List<string> { $"Error: {ex.Message}" },
                action = "", 

            };

            return Json(response);
        }
    }

    [HttpPost("UpdateViolation")] 
    public IActionResult UpdateViolation(int? id, CarsViolations model) //int? id, CarsViolation model
    {
		if (!Can("transports-admin|transports-department"))
			return StatusCode(403);


		var violationToUpdate = _context.CarsViolations.FirstOrDefault(a => a.Id == id);

        if (violationToUpdate == null)
            return StatusCode(404);
        


        violationToUpdate.CarId = model.CarId;
        violationToUpdate.Reference = model.Reference;
        violationToUpdate.Cost = model.Cost;
        violationToUpdate.PaidAt = model.PaidAt;
        violationToUpdate.ReceiptNo = model.ReceiptNo;
        violationToUpdate.StaffId = model.StaffId;
        violationToUpdate.Location = model.Location;
        violationToUpdate.Date = model.Date;
        violationToUpdate.Description = model.Description;
        violationToUpdate.UpdatedBy = Auth().EmpNo;

        try
        {
            _context.SaveChangesAsync();

            var response = new
            {
                success = true,
                message = new List<string> { "Violation record updated successfully." },
                action = "reload",  
            };

       
            return Json(response);
        }
        catch (Exception ex)
        {
            var response = new
            {
                success = false,
                message = new List<string> { $"Error: {ex.Message}" },
                action = "",  

            };


            return Json(response);
        }
    }

    [HttpGet("DeleteService/{id}")]
    public IActionResult DeleteService(int? id)
    {
		if (!Can("transports-admin|transports-department"))
			return StatusCode(403);


		if (id == null)
            return NotFound();
        

        var service = _context.CarsServices.Find(id);

        if (service == null)
            return NotFound();
        

        service.Deleted01 = 1;

        _context.SaveChanges();

        var redirectUrl = $"/Transports/ViewCar/{service.CarId}";

        return Redirect(redirectUrl);
    }


    [HttpGet("DeleteService2/{id}")]
    public IActionResult DeleteService2(int? id)
    {
		if (!Can("transports-admin|transports-department"))
			return StatusCode(403);

		if (id == null)
            return NotFound();


        var service = _context.CarsServices.Find(id);

        if (service == null)
            return NotFound();
        

        service.Deleted01 = 1;

        _context.SaveChanges();

        var redirectUrl = $"/Transports/Service";

        return Redirect(redirectUrl);
    }


    [HttpGet("DeleteGas/{id}")]
    public IActionResult DeleteGas(int? id)
    {
		if (!Can("transports-admin|transports-department"))
			return StatusCode(403);


		if (id == null)
            return NotFound();
        
        var gas = _context.CarsGasDtl.Find(id);

        if (gas == null)
            return NotFound();
        

        gas.Deleted01 = 1;

        _context.SaveChanges();

        var redirectUrl = $"/Transports/ViewCar/{gas.CarId}";

        return Redirect(redirectUrl);
    }

    [HttpGet("DeleteGas2/{id}")]
    public IActionResult DeleteGas2(int? id)
    {

		if (!Can("transports-admin|transports-department"))
			return StatusCode(403);


		if (id == null)
            return NotFound();
        

        var gas = _context.CarsGasDtl.Find(id);
        if (gas == null)
            return NotFound();
        

        gas.Deleted01 = 1;


        _context.SaveChanges();


        var redirectUrl = $"/Transports/Gas/";

        return Redirect(redirectUrl);
    }

    [HttpGet("DeleteViolation2/{id}")]
    public IActionResult DeleteViolation2(int? id)
    {

        if (!Can("transports-admin|transports-department"))
            return StatusCode(403);


        var identity = Identity;

        if (id == null)
            return NotFound();


        var violation = _context.CarsViolations.Find(id);
        if (violation == null)
            return NotFound();


        violation.Deleted01 = 1;
        violation.DeletedBy = Auth().EmpNo;

        _context.SaveChanges();

        var redirectUrl = $"/Transports/Violations";

        return Redirect(redirectUrl);
    }


    [HttpGet("DeleteViolation/{id}")]
    public IActionResult DeleteViolation(int? id)
    {

		if (!Can("transports-admin|transports-department"))
			return StatusCode(403);


		var identity = Identity;

        if (id == null)
            return NotFound();
        

        var violation = _context.CarsViolations.Find(id);
        if (violation == null)
            return NotFound();
        

        violation.Deleted01 = 1;
        violation.DeletedBy = Auth().EmpNo ;

        _context.SaveChanges();

        var redirectUrl = $"/Transports/ViewCar/{violation.CarId}";

        return Redirect(redirectUrl);
    }


    [HttpGet("DeleteCar/{id}")]
    public IActionResult DeleteCar(int? id)
    {

        if (!Can("transports-admin|transports-department"))
            return StatusCode(403);


    

        if (id == null)
            return NotFound();


        var car = _context.Cars.Find(id);
        if (car == null)
            return NotFound();


        car.Deleted01 = 1;
        car.DeletedBy = Auth().EmpNo.Value;

        _context.SaveChanges();

        var redirectUrl = $"/Transports/Index";

        return Redirect(redirectUrl);
    }

    [HttpGet("DeleteMyRequest/{id}")]
    public IActionResult DeleteMyRequest(int? id)
    {

        if (id == null)
            return NotFound();


        var request = _context.CarsRequests.Where(r => r.StaffId == Auth().EmpNo && r.Id == id && r.Status == "New").FirstOrDefault();
        if (request == null)
            return NotFound();


        request.Deleted01 = 1;
        request.UpdatedBy = Auth().EmpNo.Value;

        _context.SaveChanges();

        var redirectUrl = $"/Transports/MyRequests";

        return Redirect(redirectUrl);
    }


    [HttpGet("GetStaffByDate")]
    public IActionResult GetStaffByDate(DateTime date, int car_id)
    {


        List<VempDtl> list = new List<VempDtl>();


        var req = _db.CarsRequests.Where(r => r.FromDate <= date && r.ToDate >= date && r.CarId == car_id).ToList();

        foreach(var item in req){
            list.Add(_h.StaffData(item.StaffId));
        }

        var response = new
        {
            success = true,
            data = list,
          
        };

        return Json(response);
    }

}


